import { useState } from 'react'
import { <PERSON>, Button, Paper, Typography } from '@mui/material'
import { Add, EventNote } from '@mui/icons-material'
import SessionList from './SessionList'
import CreateSessionModal from './CreateSessionModal'

function Sessions({ sessions, players, onCreateSession, onRemoveSession }) {
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleOpenModal = () => {
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handleCreateSession = (sessionName, selectedPlayerIds) => {
    onCreateSession(sessionName, selectedPlayerIds)
  }

  return (
    <Box>
      {/* Create Session Section */}
      <Paper
        elevation={3}
        sx={{
          p: { xs: 3, sm: 4 },
          mb: { xs: 3, sm: 5 },
          background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Typography
          variant="h4"
          component="h2"
          gutterBottom
          sx={{
            mb: 3,
            color: 'text.primary',
            fontWeight: 600,
          }}
        >
          Create New Session
        </Typography>
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            alignItems: 'center',
            flexDirection: { xs: 'column', sm: 'row' },
          }}
        >
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ flex: 1, textAlign: { xs: 'center', sm: 'left' } }}
          >
            Start a new poker session and add players to join the game.
          </Typography>
          <Button
            variant="contained"
            size="large"
            startIcon={<Add />}
            onClick={handleOpenModal}
            sx={{
              minWidth: { xs: '100%', sm: 180 },
              height: 56,
              fontSize: '1rem',
            }}
          >
            Create Session
          </Button>
        </Box>
      </Paper>

      {/* Sessions List */}
      <SessionList
        sessions={sessions}
        players={players}
        onRemoveSession={onRemoveSession}
      />

      {/* Create Session Modal */}
      <CreateSessionModal
        open={isModalOpen}
        onClose={handleCloseModal}
        onCreateSession={handleCreateSession}
        players={players}
      />
    </Box>
  )
}

export default Sessions
