const API_BASE_URL = 'http://localhost:3001/api';

// Generic API request function
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`API request failed: ${endpoint}`, error);
    throw error;
  }
}

// Players API
export const playersApi = {
  // Get all players
  getAll: () => apiRequest('/players'),
  
  // Get player by ID
  getById: (id) => apiRequest(`/players/${id}`),
  
  // Create new player
  create: (playerData) => apiRequest('/players', {
    method: 'POST',
    body: JSON.stringify(playerData),
  }),
  
  // Update player
  update: (id, playerData) => apiRequest(`/players/${id}`, {
    method: 'PUT',
    body: JSON.stringify(playerData),
  }),
  
  // Delete player
  delete: (id) => apiRequest(`/players/${id}`, {
    method: 'DELETE',
  }),
};

// Sessions API
export const sessionsApi = {
  // Get all sessions
  getAll: () => apiRequest('/sessions'),
  
  // Get session by ID
  getById: (id) => apiRequest(`/sessions/${id}`),
  
  // Create new session
  create: (sessionData) => apiRequest('/sessions', {
    method: 'POST',
    body: JSON.stringify(sessionData),
  }),
  
  // Update session
  update: (id, sessionData) => apiRequest(`/sessions/${id}`, {
    method: 'PUT',
    body: JSON.stringify(sessionData),
  }),
  
  // Delete session
  delete: (id) => apiRequest(`/sessions/${id}`, {
    method: 'DELETE',
  }),
};

// Health check
export const healthCheck = () => apiRequest('/health');
