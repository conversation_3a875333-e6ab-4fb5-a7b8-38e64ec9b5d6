import { useState, useEffect } from 'react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import { CssBaseline, Container, Typography, Box, Tabs, Tab } from '@mui/material'
import { Groups, EventNote } from '@mui/icons-material'
import PlayerList from './components/PlayerList'
import AddPlayerForm from './components/AddPlayerForm'
import Sessions from './components/Sessions'
import './App.css'

// Create a custom theme for the poker app
const theme = createTheme({
  palette: {
    primary: {
      main: '#667eea',
      light: '#9bb5ff',
      dark: '#3f51b5',
    },
    secondary: {
      main: '#764ba2',
      light: '#a478d4',
      dark: '#4a2c73',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
    success: {
      main: '#4caf50',
    },
    error: {
      main: '#f44336',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h3: {
      fontWeight: 700,
      fontSize: '2.5rem',
      '@media (max-width:600px)': {
        fontSize: '2rem',
      },
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h6: {
      fontWeight: 500,
      fontSize: '1.1rem',
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 12px 40px rgba(102, 126, 234, 0.2)',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 600,
          padding: '10px 24px',
        },
        contained: {
          boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
          '&:hover': {
            boxShadow: '0 6px 20px rgba(102, 126, 234, 0.4)',
            transform: 'translateY(-1px)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '&:hover fieldset': {
              borderColor: '#667eea',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#667eea',
              borderWidth: 2,
            },
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'scale(1.1)',
          },
        },
      },
    },
  },
  shape: {
    borderRadius: 8,
  },
  spacing: 8,
})

function App() {
  const [players, setPlayers] = useState([])
  const [sessions, setSessions] = useState([])
  const [activeTab, setActiveTab] = useState(0)

  // Load players from localStorage on component mount
  useEffect(() => {
    const savedPlayers = localStorage.getItem('pokerPlayers')
    if (savedPlayers) {
      setPlayers(JSON.parse(savedPlayers))
    }
  }, [])

  // Load sessions from localStorage on component mount
  useEffect(() => {
    const savedSessions = localStorage.getItem('pokerSessions')
    if (savedSessions) {
      setSessions(JSON.parse(savedSessions))
    }
  }, [])

  // Save players to localStorage whenever players array changes
  useEffect(() => {
    localStorage.setItem('pokerPlayers', JSON.stringify(players))
  }, [players])

  // Save sessions to localStorage whenever sessions array changes
  useEffect(() => {
    localStorage.setItem('pokerSessions', JSON.stringify(sessions))
  }, [sessions])

  const addPlayer = (name) => {
    if (name.trim() && !players.some(player => player.name.toLowerCase() === name.toLowerCase())) {
      const newPlayer = {
        id: Date.now() + Math.random(), // Simple ID generation
        name: name.trim()
      }
      setPlayers([...players, newPlayer])
    }
  }

  const removePlayer = (id) => {
    setPlayers(players.filter(player => player.id !== id))
  }

  const renamePlayer = (id, newName) => {
    if (newName.trim() && !players.some(player => player.id !== id && player.name.toLowerCase() === newName.toLowerCase())) {
      setPlayers(players.map(player =>
        player.id === id ? { ...player, name: newName.trim() } : player
      ))
    }
  }

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue)
  }

  const addSession = (sessionName, selectedPlayerIds) => {
    if (sessionName.trim()) {
      const newSession = {
        id: Date.now() + Math.random(), // Simple ID generation
        name: sessionName.trim(),
        createdAt: new Date().toISOString(),
        playerIds: selectedPlayerIds || []
      }
      setSessions([...sessions, newSession])
    }
  }

  const removeSession = (id) => {
    setSessions(sessions.filter(session => session.id !== id))
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          py: { xs: 2, sm: 4 },
          px: { xs: 1, sm: 2 },
        }}
      >
        <Container maxWidth="xl" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
          <Box
            textAlign="center"
            mb={{ xs: 3, sm: 5 }}
            sx={{
              color: 'white',
            }}
          >
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              sx={{
                textShadow: '2px 2px 8px rgba(0,0,0,0.3)',
                mb: 2,
              }}
            >
              🃏 Poker Player Manager
            </Typography>
            <Typography
              variant="h6"
              sx={{
                opacity: 0.95,
                fontWeight: 400,
              }}
            >
              Manage your poker night players with style
            </Typography>
          </Box>

          <Box
            sx={{
              backgroundColor: 'background.paper',
              borderRadius: 4,
              boxShadow: '0 20px 60px rgba(0,0,0,0.15)',
              backdropFilter: 'blur(10px)',
              overflow: 'hidden',
            }}
          >
            {/* Navigation Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                aria-label="poker app navigation tabs"
                sx={{
                  px: { xs: 2, sm: 3 },
                  '& .MuiTab-root': {
                    minHeight: 64,
                    fontSize: '1rem',
                    fontWeight: 600,
                  },
                }}
              >
                <Tab
                  icon={<Groups />}
                  label="Players"
                  iconPosition="start"
                  sx={{ gap: 1 }}
                />
                <Tab
                  icon={<EventNote />}
                  label="Sessions"
                  iconPosition="start"
                  sx={{ gap: 1 }}
                />
              </Tabs>
            </Box>

            {/* Tab Content */}
            <Box sx={{ p: { xs: 3, sm: 5 } }}>
              {activeTab === 0 && (
                <Box>
                  <AddPlayerForm onAddPlayer={addPlayer} />
                  <PlayerList
                    players={players}
                    onRemovePlayer={removePlayer}
                    onRenamePlayer={renamePlayer}
                  />
                </Box>
              )}
              {activeTab === 1 && (
                <Sessions
                  sessions={sessions}
                  players={players}
                  onCreateSession={addSession}
                  onRemoveSession={removeSession}
                />
              )}
            </Box>
          </Box>
        </Container>
      </Box>
    </ThemeProvider>
  )
}

export default App
