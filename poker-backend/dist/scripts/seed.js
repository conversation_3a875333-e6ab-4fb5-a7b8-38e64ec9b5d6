"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedDatabase = seedDatabase;
const db_1 = __importDefault(require("../database/db"));
const samplePlayers = [
    { name: '<PERSON>' },
    { name: '<PERSON>' },
    { name: '<PERSON>' },
    { name: '<PERSON>' },
    { name: '<PERSON>' }
];
const sampleSessions = [
    {
        name: 'Friday Night Poker',
        scheduled_datetime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        playerIds: [1, 2, 3]
    },
    {
        name: 'Weekend Tournament',
        scheduled_datetime: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        playerIds: [2, 3, 4, 5]
    },
    {
        name: 'Monthly Championship',
        scheduled_datetime: null,
        playerIds: [1, 3, 5]
    }
];
async function seedDatabase() {
    console.log('🌱 Starting database seeding...');
    try {
        await new Promise((resolve, reject) => {
            db_1.default.run('DELETE FROM session_players', (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        await new Promise((resolve, reject) => {
            db_1.default.run('DELETE FROM sessions', (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        await new Promise((resolve, reject) => {
            db_1.default.run('DELETE FROM players', (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        console.log('✅ Cleared existing data');
        const playerIds = [];
        for (const player of samplePlayers) {
            const playerId = await new Promise((resolve, reject) => {
                db_1.default.run('INSERT INTO players (name) VALUES (?)', [player.name], function (err) {
                    if (err)
                        reject(err);
                    else
                        resolve(this.lastID);
                });
            });
            playerIds.push(playerId);
            console.log(`✅ Created player: ${player.name} (ID: ${playerId})`);
        }
        for (const session of sampleSessions) {
            const sessionId = await new Promise((resolve, reject) => {
                db_1.default.run('INSERT INTO sessions (name, scheduled_datetime) VALUES (?, ?)', [session.name, session.scheduled_datetime], function (err) {
                    if (err)
                        reject(err);
                    else
                        resolve(this.lastID);
                });
            });
            console.log(`✅ Created session: ${session.name} (ID: ${sessionId})`);
            for (const playerIndex of session.playerIds) {
                if (playerIds[playerIndex - 1]) {
                    await new Promise((resolve, reject) => {
                        db_1.default.run('INSERT INTO session_players (session_id, player_id) VALUES (?, ?)', [sessionId, playerIds[playerIndex - 1]], (err) => {
                            if (err)
                                reject(err);
                            else
                                resolve();
                        });
                    });
                }
            }
            console.log(`✅ Added ${session.playerIds.length} players to session: ${session.name}`);
        }
        console.log('🎉 Database seeding completed successfully!');
        console.log(`📊 Created ${playerIds.length} players and ${sampleSessions.length} sessions`);
    }
    catch (error) {
        console.error('❌ Error seeding database:', error);
    }
    finally {
        db_1.default.close((err) => {
            if (err) {
                console.error('Error closing database:', err);
            }
            else {
                console.log('Database connection closed');
            }
            process.exit(0);
        });
    }
}
if (require.main === module) {
    seedDatabase();
}
//# sourceMappingURL=seed.js.map