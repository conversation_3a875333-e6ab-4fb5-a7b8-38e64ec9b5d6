"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
app.use((0, cors_1.default)());
app.use(express_1.default.json());
const players_1 = __importDefault(require("./routes/players"));
const sessions_1 = __importDefault(require("./routes/sessions"));
app.use('/api/players', players_1.default);
app.use('/api/sessions', sessions_1.default);
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', message: 'Poker Backend API is running' });
});
app.use((err, req, res, next) => {
    console.error(err.stack);
    const statusCode = err.statusCode || 500;
    res.status(statusCode).json({ error: err.message || 'Something went wrong!' });
});
app.use((req, res) => {
    res.status(404).json({ error: 'Route not found' });
});
app.listen(PORT, () => {
    console.log(`🃏 Poker Backend API running on port ${PORT}`);
});
exports.default = app;
//# sourceMappingURL=server.js.map