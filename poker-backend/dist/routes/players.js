"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = __importDefault(require("../database/db"));
const router = express_1.default.Router();
router.get('/', (req, res) => {
    const sql = 'SELECT * FROM players ORDER BY created_at DESC';
    db_1.default.all(sql, [], (err, rows) => {
        if (err) {
            console.error('Error fetching players:', err.message);
            res.status(500).json({ error: 'Failed to fetch players' });
        }
        else {
            res.json(rows);
        }
    });
});
router.get('/:id', (req, res) => {
    const { id } = req.params;
    const sql = 'SELECT * FROM players WHERE id = ?';
    db_1.default.get(sql, [id], (err, row) => {
        if (err) {
            console.error('Error fetching player:', err.message);
            res.status(500).json({ error: 'Failed to fetch player' });
        }
        else if (!row) {
            res.status(404).json({ error: 'Player not found' });
        }
        else {
            res.json(row);
        }
    });
});
router.post('/', (req, res) => {
    const { name } = req.body;
    if (!name || !name.trim()) {
        res.status(400).json({ error: 'Player name is required' });
        return;
    }
    const sql = 'INSERT INTO players (name) VALUES (?)';
    db_1.default.run(sql, [name.trim()], function (err) {
        if (err) {
            if (err.message.includes('UNIQUE constraint failed')) {
                res.status(409).json({ error: 'Player name already exists' });
            }
            else {
                console.error('Error creating player:', err.message);
                res.status(500).json({ error: 'Failed to create player' });
            }
        }
        else {
            db_1.default.get('SELECT * FROM players WHERE id = ?', [this.lastID], (err, row) => {
                if (err) {
                    console.error('Error fetching created player:', err.message);
                    res.status(500).json({ error: 'Player created but failed to fetch' });
                }
                else {
                    res.status(201).json(row);
                }
            });
        }
    });
});
router.put('/:id', (req, res) => {
    const { id } = req.params;
    const { name } = req.body;
    if (!name || !name.trim()) {
        res.status(400).json({ error: 'Player name is required' });
        return;
    }
    const sql = 'UPDATE players SET name = ? WHERE id = ?';
    db_1.default.run(sql, [name.trim(), id], function (err) {
        if (err) {
            if (err.message.includes('UNIQUE constraint failed')) {
                res.status(409).json({ error: 'Player name already exists' });
            }
            else {
                console.error('Error updating player:', err.message);
                res.status(500).json({ error: 'Failed to update player' });
            }
        }
        else if (this.changes === 0) {
            res.status(404).json({ error: 'Player not found' });
        }
        else {
            db_1.default.get('SELECT * FROM players WHERE id = ?', [id], (err, row) => {
                if (err) {
                    console.error('Error fetching updated player:', err.message);
                    res.status(500).json({ error: 'Player updated but failed to fetch' });
                }
                else {
                    res.json(row);
                }
            });
        }
    });
});
router.delete('/:id', (req, res) => {
    const { id } = req.params;
    const sql = 'DELETE FROM players WHERE id = ?';
    db_1.default.run(sql, [id], function (err) {
        if (err) {
            console.error('Error deleting player:', err.message);
            res.status(500).json({ error: 'Failed to delete player' });
        }
        else if (this.changes === 0) {
            res.status(404).json({ error: 'Player not found' });
        }
        else {
            res.json({ message: 'Player deleted successfully' });
        }
    });
});
exports.default = router;
//# sourceMappingURL=players.js.map