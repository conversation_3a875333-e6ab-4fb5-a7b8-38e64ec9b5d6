"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const db_1 = __importDefault(require("../database/db"));
const router = express_1.default.Router();
function generateSessionNameFromDateTime(dateTimeString) {
    const date = new Date(dateTimeString);
    const options = {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        hour12: true
    };
    return date.toLocaleDateString('en-US', options);
}
router.get('/', (req, res) => {
    const sql = `
    SELECT 
      s.*,
      GROUP_CONCAT(p.id) as player_ids,
      GROUP_CONCAT(p.name) as player_names
    FROM sessions s
    LEFT JOIN session_players sp ON s.id = sp.session_id
    LEFT JOIN players p ON sp.player_id = p.id
    GROUP BY s.id
    ORDER BY s.created_at DESC
  `;
    db_1.default.all(sql, [], (err, rows) => {
        if (err) {
            console.error('Error fetching sessions:', err.message);
            res.status(500).json({ error: 'Failed to fetch sessions' });
        }
        else {
            const sessions = rows.map(row => ({
                id: row.id,
                name: row.name,
                scheduledDateTime: row.scheduled_datetime,
                createdAt: row.created_at,
                playerIds: row.player_ids ? row.player_ids.split(',').map(id => parseInt(id)) : []
            }));
            res.json(sessions);
        }
    });
});
router.get('/:id', (req, res) => {
    const { id } = req.params;
    const sql = `
    SELECT 
      s.*,
      GROUP_CONCAT(p.id) as player_ids,
      GROUP_CONCAT(p.name) as player_names
    FROM sessions s
    LEFT JOIN session_players sp ON s.id = sp.session_id
    LEFT JOIN players p ON sp.player_id = p.id
    WHERE s.id = ?
    GROUP BY s.id
  `;
    db_1.default.get(sql, [id], (err, row) => {
        if (err) {
            console.error('Error fetching session:', err.message);
            res.status(500).json({ error: 'Failed to fetch session' });
        }
        else if (!row) {
            res.status(404).json({ error: 'Session not found' });
        }
        else {
            const session = {
                id: row.id,
                name: row.name,
                scheduledDateTime: row.scheduled_datetime,
                createdAt: row.created_at,
                playerIds: row.player_ids ? row.player_ids.split(',').map(id => parseInt(id)) : []
            };
            res.json(session);
        }
    });
});
router.post('/', (req, res) => {
    const { name, scheduledDateTime, playerIds } = req.body;
    if (!scheduledDateTime) {
        res.status(400).json({ error: 'Scheduled date and time is required' });
        return;
    }
    const sessionName = name?.trim() || generateSessionNameFromDateTime(scheduledDateTime);
    const sql = 'INSERT INTO sessions (name, scheduled_datetime) VALUES (?, ?)';
    db_1.default.run(sql, [sessionName, scheduledDateTime], function (err) {
        if (err) {
            console.error('Error creating session:', err.message);
            res.status(500).json({ error: 'Failed to create session' });
        }
        else {
            const sessionId = this.lastID;
            if (playerIds && playerIds.length > 0) {
                addPlayersToSession(sessionId, playerIds, (err) => {
                    if (err) {
                        console.error('Error adding players to session:', err.message);
                        res.status(500).json({ error: 'Session created but failed to add players' });
                    }
                    else {
                        fetchSessionById(sessionId, res);
                    }
                });
            }
            else {
                fetchSessionById(sessionId, res);
            }
        }
    });
});
router.put('/:id', (req, res) => {
    const { id } = req.params;
    const { name, scheduledDateTime, playerIds } = req.body;
    if (!scheduledDateTime) {
        res.status(400).json({ error: 'Scheduled date and time is required' });
        return;
    }
    const sessionName = name?.trim() || generateSessionNameFromDateTime(scheduledDateTime);
    const sql = 'UPDATE sessions SET name = ?, scheduled_datetime = ? WHERE id = ?';
    db_1.default.run(sql, [sessionName, scheduledDateTime, id], function (err) {
        if (err) {
            console.error('Error updating session:', err.message);
            res.status(500).json({ error: 'Failed to update session' });
        }
        else if (this.changes === 0) {
            res.status(404).json({ error: 'Session not found' });
        }
        else {
            db_1.default.run('DELETE FROM session_players WHERE session_id = ?', [id], (err) => {
                if (err) {
                    console.error('Error removing session players:', err.message);
                    res.status(500).json({ error: 'Failed to update session players' });
                }
                else {
                    if (playerIds && playerIds.length > 0) {
                        addPlayersToSession(parseInt(id), playerIds, (err) => {
                            if (err) {
                                console.error('Error adding players to session:', err.message);
                                res.status(500).json({ error: 'Session updated but failed to update players' });
                            }
                            else {
                                fetchSessionById(parseInt(id), res);
                            }
                        });
                    }
                    else {
                        fetchSessionById(parseInt(id), res);
                    }
                }
            });
        }
    });
});
router.delete('/:id', (req, res) => {
    const { id } = req.params;
    const sql = 'DELETE FROM sessions WHERE id = ?';
    db_1.default.run(sql, [id], function (err) {
        if (err) {
            console.error('Error deleting session:', err.message);
            res.status(500).json({ error: 'Failed to delete session' });
        }
        else if (this.changes === 0) {
            res.status(404).json({ error: 'Session not found' });
        }
        else {
            res.json({ message: 'Session deleted successfully' });
        }
    });
});
function addPlayersToSession(sessionId, playerIds, callback) {
    if (!playerIds || playerIds.length === 0) {
        return callback(null);
    }
    const placeholders = playerIds.map(() => '(?, ?)').join(', ');
    const sql = `INSERT INTO session_players (session_id, player_id) VALUES ${placeholders}`;
    const params = [];
    playerIds.forEach(playerId => {
        params.push(sessionId, playerId);
    });
    db_1.default.run(sql, params, callback);
}
function fetchSessionById(sessionId, res) {
    const sql = `
    SELECT 
      s.*,
      GROUP_CONCAT(p.id) as player_ids,
      GROUP_CONCAT(p.name) as player_names
    FROM sessions s
    LEFT JOIN session_players sp ON s.id = sp.session_id
    LEFT JOIN players p ON sp.player_id = p.id
    WHERE s.id = ?
    GROUP BY s.id
  `;
    db_1.default.get(sql, [sessionId], (err, row) => {
        if (err) {
            console.error('Error fetching created/updated session:', err.message);
            res.status(500).json({ error: 'Session saved but failed to fetch' });
        }
        else if (row) {
            const session = {
                id: row.id,
                name: row.name,
                scheduledDateTime: row.scheduled_datetime,
                createdAt: row.created_at,
                playerIds: row.player_ids ? row.player_ids.split(',').map(id => parseInt(id)) : []
            };
            res.status(201).json(session);
        }
        else {
            res.status(500).json({ error: 'Session saved but not found' });
        }
    });
}
exports.default = router;
//# sourceMappingURL=sessions.js.map