{"name": "@types/http-errors", "version": "2.0.5", "description": "TypeScript definitions for http-errors", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/http-errors"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "621b9125a6493a2fa928b9150e335cb57429fb00e3bc0257426f1173903f7a4a", "typeScriptVersion": "5.1"}